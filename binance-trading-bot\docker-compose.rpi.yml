version: '3.7'

services:
  binance-bot:
    container_name: binance-bot
    image: chrisleekr/binance-trading-bot:latest
    networks:
      - internal
    env_file:
      - .env
    restart: unless-stopped
    environment:
      # - BINANCE_MODE=test
      - BINANCE_MODE=live
      - BINANCE_REDIS_HOST=binance-redis
      - BINANCE_REDIS_PORT=6379
      - BINANCE_REDIS_PASSWORD=secretp422
      - BINANCE_LOG_LEVEL=INFO
    ports:
      - 8080:80
    logging:
      driver: 'json-file'
      options:
        max-size: '50m'

  tradingview:
    container_name: tradingview
    image: chrisleekr/binance-trading-bot:tradingview
    networks:
      - internal
    restart: unless-stopped
    environment:
      # https://docs.python.org/3/howto/logging.html#logging-levels
      - BINANCE_TRADINGVIEW_LOG_LEVEL=INFO
    logging:
      driver: 'json-file'
      options:
        max-size: '50m'

  binance-redis:
    container_name: binance-redis
    # From Redis 6.2.6 onwards, it does not support Raspberry Pi 32bit.
    image: redis:6.2.4
    sysctls:
      net.core.somaxconn: 1024
    networks:
      - internal
    restart: unless-stopped
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command:
      redis-server /usr/local/etc/redis/redis.conf --requirepass secretp422

  binance-mongo:
    container_name: binance-mongo
    image: apcheamitru/arm32v7-mongo:3.2.20
    restart: unless-stopped
    networks:
      - internal
    volumes:
      - mongo_data:/data/db
    #command: mongod --repair

networks:
  internal:
    driver: bridge
    # driver_opts:
    #     com.docker.network.driver.mtu: 1442

volumes:
  redis_data:
  mongo_data:
