name: Bump versions

on: [workflow_dispatch]

jobs:
  bump-versions:
    name: Bump versions
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source code
        uses: actions/checkout@v2
      - id: bump-version
        name: Bump Version (Patch)
        if:
          github.repository == 'chrisleekr/binance-trading-bot' && github.ref ==
          'refs/heads/master'
        run: |
          git config --global user.email "${{ secrets.GIT_CI_EMAIL }}"
          git config --global user.name "$GITHUB_ACTOR"
          PACKAGE_VERSION=$(npm version patch -m "bump: update version to %s [skip ci]")
          git push https://$GITHUB_ACTOR:${{ secrets.GIT_CI_PASSWORD }}@${{ secrets.GIT_REPO_DOMAIN }}/chrisleekr/binance-trading-bot HEAD:$GITHUB_REF
          git push https://$GITHUB_ACTOR:${{ secrets.GIT_CI_PASSWORD }}@${{ secrets.GIT_REPO_DOMAIN }}/chrisleekr/binance-trading-bot $PACKAGE_VERSION
          echo "::set-output name=PACKAGE_VERSION::$PACKAGE_VERSION"
      - name: Trigger release workflow
        uses: peter-evans/repository-dispatch@v1
        with:
          token: ${{ secrets.GIT_REPO_ACCESS_TOKEN }}
          event-type: Release & Docker
          client-payload:
            '{"ref": "${{ steps.bump-version.outputs.PACKAGE_VERSION }}"}'
