## Live
BINANCE_LIVE_API_KEY=
BINANCE_LIVE_SECRET_KEY=

## Test
BINANCE_TEST_API_KEY=
BINANCE_TEST_SECRET_KEY=

## Slack
BINANCE_SLACK_ENABLED=false
BINANCE_SLACK_WEBHOOK_URL=
BINANCE_SLACK_CHANNEL=
BINANCE_SLACK_USERNAME=

## Local Tunnel
BINANCE_LOCAL_TUNNEL_ENABLED=false
### A local tunnel makes the bot accessible from the outside.
### Please set the subdomain of the local tunnel as a subdomain that only you can remember.
BINANCE_LOCAL_TUNNEL_SUBDOMAIN=default

## Feature Toggles
BINANCE_FEATURE_TOGGLE_NOTIFY_ORDER_CONFIRM=true
BINANCE_FEATURE_TOGGLE_NOTIFY_DEBUG=false
BINANCE_FEATURE_TOGGLE_NOTIFY_ORDER_EXECUTE=true

## Authentication
BINANCE_AUTHENTICATION_ENABLED=true
### Please set your own password.
BINANCE_AUTHENTICATION_PASSWORD=123456
BINANCE_JOBS_TRAILING_TRADE_BOT_OPTIONS_AUTHENTICATION_LOCK_LIST=true
BINANCE_JOBS_TRAILING_TRADE_BOT_OPTIONS_AUTHENTICATION_LOCK_AFTER=120
