const config = require('config');
const axios = require('axios');
const slack = require('../slack');

jest.mock('config');
jest.mock('axios');

describe('slack', () => {
  let result;

  describe('sendMessage', () => {
    describe('when slack is not enabled', () => {
      beforeEach(async () => {
        config.get = jest.fn(key => {
          switch (key) {
            case 'slack.enabled':
              return false;
            default:
              return '';
          }
        });

        result = await slack.sendMessage('my message');
      });

      it('returns expected value', () => {
        expect(result).toStrictEqual({});
      });
    });

    describe('when slack is enabled', () => {
      beforeEach(async () => {
        process.env.NODE_ENV = 'live';

        config.get = jest.fn(key => {
          switch (key) {
            case 'slack.enabled':
              return true;
            default:
              return `value-${key}`;
          }
        });

        axios.post = jest.fn().mockResolvedValue(true);

        result = await slack.sendMessage('my message', {
          symbol: 'BTCUSDT',
          apiLimit: 10
        });
      });

      it('triggers axios.post', () => {
        expect(axios.post).toHaveBeenCalledWith('value-slack.webhookUrl', {
          channel: 'value-slack.channel',
          text: expect.stringContaining('my message'),
          type: 'mrkdwn',
          username: 'value-slack.username - value-mode'
        });
      });

      it('returns expected value', () => {
        expect(result).toBeTruthy();
      });

      describe('if duplicated', () => {
        beforeEach(async () => {
          axios.post.mockClear();
          result = await slack.sendMessage('my message', {
            symbol: 'BTCUSDT',
            apiLimit: 10
          });
        });

        it('does not trigger axios.post', () => {
          expect(axios.post).not.toHaveBeenCalled();
        });
      });

      describe('when post throws an error', () => {
        beforeEach(async () => {
          axios.post.mockReset();

          axios.post = jest
            .fn()
            .mockRejectedValue(new Error('something happened'));

          result = await slack.sendMessage('my message - something happened', {
            symbol: 'BTCUSDT',
            apiLimit: 10
          });
        });

        it('returns expected value', () => {
          expect(result).toStrictEqual({});
        });
      });
    });
  });
});
