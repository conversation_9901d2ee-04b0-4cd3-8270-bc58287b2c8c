/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-no-undef */
/* eslint-disable no-undef */
class SettingIcon extends React.Component {
  constructor(props) {
    super(props);

    this.modalToStateMap = {
      setting: 'showSettingModal',
      confirm: 'showConfirmModal'
    };

    this.state = {
      showSettingModal: false,
      showConfirmModal: false,
      quoteAssets: [],
      minNotionals: {},
      configuration: {},
      rawConfiguration: {},
      validation: {},
      exchangeSymbols: {}
    };

    this.handleModalShow = this.handleModalShow.bind(this);
    this.handleModalClose = this.handleModalClose.bind(this);

    this.handleFormSubmit = this.handleFormSubmit.bind(this);
    this.handleInputChange = this.handleInputChange.bind(this);
    this.handleGridTradeChange = this.handleGridTradeChange.bind(this);
    this.handleLastBuyPriceRemoveThresholdChange =
      this.handleLastBuyPriceRemoveThresholdChange.bind(this);
    this.handleBotOptionsChange = this.handleBotOptionsChange.bind(this);

    this.handleSetValidation = this.handleSetValidation.bind(this);
    this.symbolsTypeaheadRef = React.createRef();
  }

  getQuoteAssets(
    exchangeSymbols,
    selectedSymbols,
    lastBuyPriceRemoveThresholds
  ) {
    const quoteAssets = [];

    const minNotionals = {};

    selectedSymbols.forEach(symbol => {
      const symbolInfo = exchangeSymbols[symbol];
      if (symbolInfo === undefined) {
        return;
      }
      const { quoteAsset, minNotional } = symbolInfo;
      if (quoteAssets.includes(quoteAsset) === false) {
        quoteAssets.push(quoteAsset);
        minNotionals[quoteAsset] = minNotional;
      }

      if (lastBuyPriceRemoveThresholds[quoteAsset] === undefined) {
        lastBuyPriceRemoveThresholds[quoteAsset] = minNotional;
      }
    });

    return { quoteAssets, minNotionals, lastBuyPriceRemoveThresholds };
  }

  isConfigChanged(nextProps) {
    if (
      this.state.showSettingModal === false &&
      _.isEmpty(nextProps.configuration) === false &&
      _.isEqual(nextProps.configuration, this.state.rawConfiguration) === false
    ) {
      return true;
    }

    return false;
  }

  isExchangeSymbolsChanged(nextProps) {
    if (
      _.isEmpty(nextProps.exchangeSymbols) === false &&
      _.isEqual(nextProps.exchangeSymbols, this.state.exchangeSymbols) === false
    ) {
      return true;
    }

    return false;
  }

  componentDidUpdate(nextProps) {
    if (this.isExchangeSymbolsChanged(nextProps)) {
      const { exchangeSymbols, configuration } = nextProps;
      const { symbols: selectedSymbols } = configuration;

      const { quoteAssets, minNotionals } = this.getQuoteAssets(
        exchangeSymbols,
        selectedSymbols,
        configuration.buy.lastBuyPriceRemoveThresholds
      );

      this.setState({
        quoteAssets,
        minNotionals,
        exchangeSymbols
      });
    }

    // Only update configuration, when the modal is closed and different.
    if (this.isConfigChanged(nextProps)) {
      const { configuration: rawConfiguration } = nextProps;
      const configuration = _.cloneDeep(rawConfiguration);

      if (configuration.buy.lastBuyPriceRemoveThresholds === undefined) {
        configuration.buy.lastBuyPriceRemoveThresholds = {};
      }

      this.setState({
        configuration,
        rawConfiguration
      });
    }
  }

  handleFormSubmit(extraConfiguration = {}) {
    this.handleModalClose('confirm');
    this.handleModalClose('setting');
    this.props.sendWebSocket('setting-update', {
      ...this.state.configuration,
      ...extraConfiguration
    });
  }

  componentDidMount() {
    this.props.sendWebSocket('exchange-symbols-get');
  }

  handleModalShow(modal) {
    if (modal === 'setting') {
      this.props.sendWebSocket('exchange-symbols-get');
    }

    this.setState({
      [this.modalToStateMap[modal]]: true
    });
  }

  handleModalClose(modal) {
    this.setState({
      [this.modalToStateMap[modal]]: false
    });
  }

  handleInputChange(event) {
    const target = event.target;
    const value =
      target.type === 'checkbox'
        ? target.checked
        : target.type === 'number'
        ? +target.value
        : target.value;
    const stateKey = target.getAttribute('data-state-key');

    const { configuration } = this.state;

    this.setState({
      configuration: _.set(configuration, stateKey, value)
    });
  }

  handleGridTradeChange(type, newGrid) {
    const { configuration } = this.state;

    this.setState({
      configuration: _.set(configuration, `${type}.gridTrade`, newGrid)
    });
  }

  handleLastBuyPriceRemoveThresholdChange(newLastBuyPriceRemoveThresholds) {
    const { configuration } = this.state;

    this.setState({
      configuration: _.set(
        configuration,
        'buy.lastBuyPriceRemoveThresholds',
        newLastBuyPriceRemoveThresholds
      )
    });
  }

  handleBotOptionsChange(newBotOptions) {
    const { configuration } = this.state;

    this.setState({
      configuration: _.set(configuration, 'botOptions', newBotOptions)
    });
  }

  handleSetValidation(type, isValid) {
    const { validation } = this.state;
    this.setState({ validation: { ...validation, [type]: isValid } });
  }

  render() {
    const { isAuthenticated, exchangeSymbols, tradingViewIntervals } =
      this.props;

    const { configuration, quoteAssets, minNotionals, validation } = this.state;
    const { symbols: selectedSymbols } = configuration;

    if (_.isEmpty(configuration) || isAuthenticated === false) {
      return '';
    }

    // Check validation if contains any false
    const isValid = Object.values(validation).includes(false) === false;

    return (
      <div className='header-column-icon-wrapper setting-wrapper'>
        <button
          type='button'
          className='btn btn-sm btn-link p-0 pl-1 pr-1'
          onClick={() => this.handleModalShow('setting')}>
          <i className='fas fa-cog'></i>
        </button>
        <Modal
          show={this.state.showSettingModal}
          onHide={() => this.handleModalClose('setting)')}
          size='xl'>
          <Form>
            <Modal.Header className='pt-1 pb-1'>
              <Modal.Title>Global Settings</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <span className='text-muted'>
                In this modal, you can configure the global configuration. If
                the symbol has a specific configuration, the change won't impact
                the symbol. Please make sure you understand what the setting is
                about before changing the configuration value.
              </span>
              <Accordion defaultActiveKey='0'>
                <Card className='mt-1' style={{ overflow: 'visible' }}>
                  <Card.Header className='px-2 py-1'>
                    <Accordion.Toggle
                      as={Button}
                      variant='link'
                      eventKey='0'
                      className='p-0 fs-7 text-uppercase'>
                      Symbols
                    </Accordion.Toggle>
                  </Card.Header>
                  <Accordion.Collapse eventKey='0'>
                    <Card.Body className='px-2 py-1'>
                      <div className='row'>
                        <div className='col-12'>
                          <Form.Group className='mb-2'>
                            <Typeahead
                              id='exchange-symbols-list'
                              multiple
                              onChange={selected => {
                                // Handle selections...
                                const { configuration } = this.state;

                                configuration.symbols = selected;

                                this.handleSetValidation('symbols', true);

                                if (_.isEmpty(configuration.symbols)) {
                                  this.handleSetValidation('symbols', false);
                                }

                                const {
                                  quoteAssets,
                                  minNotionals,
                                  lastBuyPriceRemoveThresholds
                                } = this.getQuoteAssets(
                                  exchangeSymbols,
                                  selected,
                                  configuration.buy.lastBuyPriceRemoveThresholds
                                );

                                configuration.buy.lastBuyPriceRemoveThresholds =
                                  lastBuyPriceRemoveThresholds;

                                this.setState({
                                  configuration,
                                  quoteAssets,
                                  minNotionals
                                });
                              }}
                              ref={this.symbolsTypeaheadRef}
                              size='sm'
                              options={_.keys(exchangeSymbols)}
                              renderMenuItemChildren={(
                                option,
                                { text },
                                _index
                              ) => (
                                <React.Fragment>
                                  <div className='d-flex justify-content-between align-items-center'>
                                    <div>
                                      <i
                                        style={{ fontSize: '0.4em' }}
                                        className={`fas fa-circle align-middle mr-2 fa-fw ${
                                          exchangeSymbols[option].status ===
                                          'TRADING'
                                            ? 'text-success blink'
                                            : 'text-danger'
                                        }`}></i>
                                      <Highlighter search={text}>
                                        {option}
                                      </Highlighter>
                                    </div>
                                    {exchangeSymbols[option].status ===
                                    'TRADING' ? (
                                      <span className='badge badge-success badge-pill'>
                                        Active
                                      </span>
                                    ) : (
                                      <span className='badge badge-danger badge-pill'>
                                        Inactive
                                      </span>
                                    )}
                                  </div>
                                </React.Fragment>
                              )}
                              defaultSelected={selectedSymbols}
                              placeholder='Choose symbols to monitor...'
                              isInvalid={
                                _.get(validation, `symbols`, true) === false
                              }
                            />
                          </Form.Group>
                        </div>
                      </div>
                      <div className='row'>
                        <div className='col-12 text-right'>
                          <button
                            type='button'
                            className='btn btn-sm btn-clear-symbols'
                            onClick={e => {
                              e.preventDefault();
                              this.symbolsTypeaheadRef.current.clear();
                              const { configuration } = this.state;
                              configuration.symbols = [];
                            }}>
                            Clear selection
                          </button>
                        </div>
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </Card>
              </Accordion>

              <Accordion defaultActiveKey='0'>
                <Card className='mt-1'>
                  <Card.Header className='px-2 py-1'>
                    <Accordion.Toggle
                      as={Button}
                      variant='link'
                      eventKey='0'
                      className='p-0 fs-7 text-uppercase'>
                      Candle Settings
                    </Accordion.Toggle>
                  </Card.Header>
                  <Accordion.Collapse eventKey='0'>
                    <Card.Body className='px-2 py-1'>
                      <div className='row'>
                        <div className='col-6'>
                          <Form.Group
                            controlId='field-candles-interval'
                            className='mb-2'>
                            <Form.Label className='mb-0'>
                              Interval
                              <OverlayTrigger
                                trigger='click'
                                key='interval-overlay'
                                placement='bottom'
                                overlay={
                                  <Popover id='interval-overlay-right'>
                                    <Popover.Content>
                                      Set candle interval for calculating the
                                      highest/lowest price.
                                    </Popover.Content>
                                  </Popover>
                                }>
                                <Button
                                  variant='link'
                                  className='p-0 m-0 ml-1 text-info'>
                                  <i className='fas fa-question-circle fa-sm'></i>
                                </Button>
                              </OverlayTrigger>
                            </Form.Label>
                            <Form.Control
                              size='sm'
                              as='select'
                              required
                              data-state-key='candles.interval'
                              value={configuration.candles.interval}
                              onChange={this.handleInputChange}>
                              <option value='1m'>1m</option>
                              <option value='3m'>3m</option>
                              <option value='5m'>5m</option>
                              <option value='15m'>15m</option>
                              <option value='30m'>30m</option>
                              <option value='1h'>1h</option>
                              <option value='2h'>2h</option>
                              <option value='4h'>4h</option>
                              <option value='1d'>1d</option>
                            </Form.Control>
                          </Form.Group>
                        </div>
                        <div className='col-6'>
                          <Form.Group
                            controlId='field-candles-limit'
                            className='mb-2'>
                            <Form.Label className='mb-0'>
                              Limit
                              <OverlayTrigger
                                trigger='click'
                                key='limit-overlay'
                                placement='bottom'
                                overlay={
                                  <Popover id='limit-overlay-right'>
                                    <Popover.Content>
                                      Set the number of candles to retrieve for
                                      calculating the highest/lowest price.
                                    </Popover.Content>
                                  </Popover>
                                }>
                                <Button
                                  variant='link'
                                  className='p-0 m-0 ml-1 text-info'>
                                  <i className='fas fa-question-circle fa-sm'></i>
                                </Button>
                              </OverlayTrigger>
                            </Form.Label>
                            <Form.Control
                              size='sm'
                              type='number'
                              placeholder='Enter limit'
                              required
                              min='0'
                              step='1'
                              data-state-key='candles.limit'
                              value={configuration.candles.limit}
                              onChange={this.handleInputChange}
                            />
                          </Form.Group>
                        </div>
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </Card>
              </Accordion>

              <Accordion defaultActiveKey='0'>
                <Card className='mt-1'>
                  <Card.Header className='px-2 py-1'>
                    <Accordion.Toggle
                      as={Button}
                      variant='link'
                      eventKey='0'
                      className='p-0 fs-7 text-uppercase'>
                      Buy Configurations
                    </Accordion.Toggle>
                  </Card.Header>
                  <Accordion.Collapse eventKey='0'>
                    <Card.Body className='px-2 py-1'>
                      <div className='row'>
                        <div className='col-12'>
                          <Form.Group
                            controlId='field-buy-enabled'
                            className='mb-2'>
                            <Form.Check size='sm'>
                              <Form.Check.Input
                                type='checkbox'
                                data-state-key='buy.enabled'
                                checked={configuration.buy.enabled}
                                onChange={this.handleInputChange}
                              />
                              <Form.Check.Label>
                                Trading Enabled{' '}
                                <OverlayTrigger
                                  trigger='click'
                                  key='buy-enabled-overlay'
                                  placement='bottom'
                                  overlay={
                                    <Popover id='buy-enabled-overlay-right'>
                                      <Popover.Content>
                                        If enabled, the bot will purchase the
                                        coin when it detects the buy signal. If
                                        disabled, the bot will not purchase the
                                        coin, but continue to monitoring. When
                                        the market is volatile, you can disable
                                        it temporarily.
                                      </Popover.Content>
                                    </Popover>
                                  }>
                                  <Button
                                    variant='link'
                                    className='p-0 m-0 ml-1 text-info'>
                                    <i className='fas fa-question-circle fa-sm'></i>
                                  </Button>
                                </OverlayTrigger>
                              </Form.Check.Label>
                            </Form.Check>
                          </Form.Group>
                        </div>
                        <div className='col-12'>
                          <SettingIconGridBuy
                            gridTrade={configuration.buy.gridTrade}
                            quoteAssets={quoteAssets}
                            minNotionals={minNotionals}
                            handleSetValidation={this.handleSetValidation}
                            handleGridTradeChange={this.handleGridTradeChange}
                          />
                        </div>
                        <div className='col-12'>
                          <hr />
                        </div>
                        <div className='col-12'>
                          <Accordion defaultActiveKey='0'>
                            <Card className='mt-1'>
                              <Card.Header className='px-2 py-1'>
                                <Accordion.Toggle
                                  as={Button}
                                  variant='link'
                                  eventKey='0'
                                  className='p-0 fs-7 text-uppercase'>
                                  Last buy price removal threshold
                                </Accordion.Toggle>
                              </Card.Header>
                              <Accordion.Collapse eventKey='0'>
                                <Card.Body className='px-2 py-1'>
                                  <div className='row'>
                                    <SettingIconLastBuyPriceRemoveThreshold
                                      quoteAssets={quoteAssets}
                                      lastBuyPriceRemoveThresholds={
                                        configuration.buy
                                          .lastBuyPriceRemoveThresholds
                                      }
                                      handleLastBuyPriceRemoveThresholdChange={
                                        this
                                          .handleLastBuyPriceRemoveThresholdChange
                                      }
                                    />
                                  </div>
                                </Card.Body>
                              </Accordion.Collapse>
                            </Card>
                          </Accordion>
                        </div>

                        <div className='col-12'>
                          <Accordion defaultActiveKey='0'>
                            <Card className='mt-1'>
                              <Card.Header className='px-2 py-1'>
                                <Accordion.Toggle
                                  as={Button}
                                  variant='link'
                                  eventKey='0'
                                  className='p-0 fs-7 text-uppercase'>
                                  Buy Restriction with ATH (All Time High)
                                </Accordion.Toggle>
                              </Card.Header>
                              <Accordion.Collapse eventKey='0'>
                                <Card.Body className='px-2 py-1'>
                                  <div className='row'>
                                    <div className='col-12'>
                                      <Form.Group
                                        controlId='field-buy-ath-restriction-enabled'
                                        className='mb-2'>
                                        <Form.Check size='sm'>
                                          <Form.Check.Input
                                            type='checkbox'
                                            data-state-key='buy.athRestriction.enabled'
                                            checked={
                                              configuration.buy.athRestriction
                                                .enabled
                                            }
                                            onChange={this.handleInputChange}
                                          />
                                          <Form.Check.Label>
                                            ATH Buy Restriction Enabled{' '}
                                            <OverlayTrigger
                                              trigger='click'
                                              key='buy-ath-restriction-enabled-overlay'
                                              placement='bottom'
                                              overlay={
                                                <Popover id='buy-ath-restriction-enabled-overlay-right'>
                                                  <Popover.Content>
                                                    If enabled, the bot will
                                                    retrieve ATH (All Time High)
                                                    price of the coin based on
                                                    the interval/candle
                                                    configuration. If the buy
                                                    trigger price is higher than
                                                    ATH buy restriction price,
                                                    which is calculated by ATH
                                                    Restriction price
                                                    percentage, the bot will not
                                                    place a buy order. The bot
                                                    will place an order when the
                                                    trigger price is lower than
                                                    ATH buy restriction price.
                                                  </Popover.Content>
                                                </Popover>
                                              }>
                                              <Button
                                                variant='link'
                                                className='p-0 m-0 ml-1 text-info'>
                                                <i className='fas fa-question-circle fa-sm'></i>
                                              </Button>
                                            </OverlayTrigger>
                                          </Form.Check.Label>
                                        </Form.Check>
                                      </Form.Group>
                                    </div>
                                    <div className='col-xs-12 col-sm-6'>
                                      <Form.Group
                                        controlId='field-ath-candles-interval'
                                        className='mb-2'>
                                        <Form.Label className='mb-0'>
                                          Interval
                                          <OverlayTrigger
                                            trigger='click'
                                            key='interval-overlay'
                                            placement='bottom'
                                            overlay={
                                              <Popover id='interval-overlay-right'>
                                                <Popover.Content>
                                                  Set candle interval for
                                                  calculating the ATH (All The
                                                  High) price.
                                                </Popover.Content>
                                              </Popover>
                                            }>
                                            <Button
                                              variant='link'
                                              className='p-0 m-0 ml-1 text-info'>
                                              <i className='fas fa-question-circle fa-sm'></i>
                                            </Button>
                                          </OverlayTrigger>
                                        </Form.Label>
                                        <Form.Control
                                          size='sm'
                                          as='select'
                                          required
                                          data-state-key='buy.athRestriction.candles.interval'
                                          value={
                                            configuration.buy.athRestriction
                                              .candles.interval
                                          }
                                          onChange={this.handleInputChange}>
                                          <option value='1m'>1m</option>
                                          <option value='3m'>3m</option>
                                          <option value='5m'>5m</option>
                                          <option value='15m'>15m</option>
                                          <option value='30m'>30m</option>
                                          <option value='1h'>1h</option>
                                          <option value='2h'>2h</option>
                                          <option value='4h'>4h</option>
                                          <option value='1d'>1d</option>
                                        </Form.Control>
                                      </Form.Group>
                                    </div>
                                    <div className='col-xs-12 col-sm-6'>
                                      <Form.Group
                                        controlId='field-ath-candles-limit'
                                        className='mb-2'>
                                        <Form.Label className='mb-0'>
                                          Limit
                                          <OverlayTrigger
                                            trigger='click'
                                            key='limit-overlay'
                                            placement='bottom'
                                            overlay={
                                              <Popover id='limit-overlay-right'>
                                                <Popover.Content>
                                                  Set the number of candles to
                                                  retrieve for calculating the
                                                  ATH (All The High) price.
                                                </Popover.Content>
                                              </Popover>
                                            }>
                                            <Button
                                              variant='link'
                                              className='p-0 m-0 ml-1 text-info'>
                                              <i className='fas fa-question-circle fa-sm'></i>
                                            </Button>
                                          </OverlayTrigger>
                                        </Form.Label>
                                        <Form.Control
                                          size='sm'
                                          type='number'
                                          placeholder='Enter limit'
                                          required
                                          min='0'
                                          step='1'
                                          data-state-key='buy.athRestriction.candles.limit'
                                          value={
                                            configuration.buy.athRestriction
                                              .candles.limit
                                          }
                                          onChange={this.handleInputChange}
                                        />
                                      </Form.Group>
                                    </div>
                                    <div className='col-xs-12 col-sm-6'>
                                      <Form.Group
                                        controlId='field-buy-restriction-percentage'
                                        className='mb-2'>
                                        <Form.Label className='mb-0'>
                                          Restriction price percentage{' '}
                                          <OverlayTrigger
                                            trigger='click'
                                            key='interval-overlay'
                                            placement='bottom'
                                            overlay={
                                              <Popover id='interval-overlay-right'>
                                                <Popover.Content>
                                                  Set the percentage to
                                                  calculate restriction price.
                                                  i.e. if set <code>0.9</code>{' '}
                                                  and the ATH(All Time High)
                                                  price <code>$110</code>,
                                                  restriction price will be{' '}
                                                  <code>$99</code> for stop
                                                  limit order.
                                                </Popover.Content>
                                              </Popover>
                                            }>
                                            <Button
                                              variant='link'
                                              className='p-0 m-0 ml-1 text-info'>
                                              <i className='fas fa-question-circle fa-sm'></i>
                                            </Button>
                                          </OverlayTrigger>
                                        </Form.Label>
                                        <Form.Control
                                          size='sm'
                                          type='number'
                                          placeholder='Enter restriction price percentage'
                                          required
                                          min='0'
                                          step='0.0001'
                                          data-state-key='buy.athRestriction.restrictionPercentage'
                                          value={
                                            configuration.buy.athRestriction
                                              .restrictionPercentage
                                          }
                                          onChange={this.handleInputChange}
                                        />
                                      </Form.Group>
                                    </div>
                                  </div>
                                </Card.Body>
                              </Accordion.Collapse>
                            </Card>
                          </Accordion>
                        </div>
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </Card>
              </Accordion>

              <Accordion defaultActiveKey='0'>
                <Card className='mt-1'>
                  <Card.Header className='px-2 py-1'>
                    <Accordion.Toggle
                      as={Button}
                      variant='link'
                      eventKey='0'
                      className='p-0 fs-7 text-uppercase'>
                      Sell Configurations
                    </Accordion.Toggle>
                  </Card.Header>
                  <Accordion.Collapse eventKey='0'>
                    <Card.Body className='px-2 py-1'>
                      <div className='row'>
                        <div className='col-12'>
                          <Form.Group
                            controlId='field-sell-enabled'
                            className='mb-2'>
                            <Form.Check size='sm'>
                              <Form.Check.Input
                                type='checkbox'
                                data-state-key='sell.enabled'
                                checked={configuration.sell.enabled}
                                onChange={this.handleInputChange}
                              />
                              <Form.Check.Label>
                                Trading Enabled{' '}
                                <OverlayTrigger
                                  trigger='click'
                                  key='sell-enabled-overlay'
                                  placement='bottom'
                                  overlay={
                                    <Popover id='sell-enabled-overlay-right'>
                                      <Popover.Content>
                                        If enabled, the bot will sell the coin
                                        when it detects the sell signal. If
                                        disabled, the bot will not sell the
                                        coin, but continue to monitoring. When
                                        the market is volatile, you can disable
                                        it temporarily.
                                      </Popover.Content>
                                    </Popover>
                                  }>
                                  <Button
                                    variant='link'
                                    className='p-0 m-0 ml-1 text-info'>
                                    <i className='fas fa-question-circle fa-sm'></i>
                                  </Button>
                                </OverlayTrigger>
                              </Form.Check.Label>
                            </Form.Check>
                          </Form.Group>
                        </div>
                        <div className='col-12'>
                          <SettingIconGridSell
                            gridTrade={configuration.sell.gridTrade}
                            quoteAssets={quoteAssets}
                            handleSetValidation={this.handleSetValidation}
                            handleGridTradeChange={this.handleGridTradeChange}
                          />
                        </div>
                        <div className='col-12'>
                          <hr />
                        </div>
                        <div className='col-12'>
                          <Accordion defaultActiveKey='0'>
                            <Card className='mt-1'>
                              <Card.Header className='px-2 py-1'>
                                <Accordion.Toggle
                                  as={Button}
                                  variant='link'
                                  eventKey='0'
                                  className='p-0 fs-7 text-uppercase'>
                                  Sell Stop-Loss
                                </Accordion.Toggle>
                              </Card.Header>
                              <Accordion.Collapse eventKey='0'>
                                <Card.Body className='px-2 py-1'>
                                  <div className='row'>
                                    <div className='col-12'>
                                      <Form.Group
                                        controlId='field-sell-stop-loss-enabled'
                                        className='mb-2'>
                                        <Form.Check size='sm'>
                                          <Form.Check.Input
                                            type='checkbox'
                                            data-state-key='sell.stopLoss.enabled'
                                            checked={
                                              configuration.sell.stopLoss
                                                .enabled
                                            }
                                            onChange={this.handleInputChange}
                                          />
                                          <Form.Check.Label>
                                            Stop-Loss Enabled{' '}
                                            <OverlayTrigger
                                              trigger='click'
                                              key='sell-stop-loss-enabled-overlay'
                                              placement='bottom'
                                              overlay={
                                                <Popover id='sell-stop-loss-enabled-overlay-right'>
                                                  <Popover.Content>
                                                    If enabled, the bot will
                                                    sell the coin when it
                                                    reaches the configured
                                                    amount of the loss from the
                                                    last buy price. You can
                                                    enable this feature to
                                                    prevent the loss more than
                                                    expected.
                                                  </Popover.Content>
                                                </Popover>
                                              }>
                                              <Button
                                                variant='link'
                                                className='p-0 m-0 ml-1 text-info'>
                                                <i className='fas fa-question-circle fa-sm'></i>
                                              </Button>
                                            </OverlayTrigger>
                                          </Form.Check.Label>
                                        </Form.Check>
                                      </Form.Group>
                                    </div>
                                    <div className='col-xs-12 col-sm-6'>
                                      <Form.Group
                                        controlId='field-sell-stop-loss-max-loss-percentage'
                                        className='mb-2'>
                                        <Form.Label className='mb-0'>
                                          Max loss percentage{' '}
                                          <OverlayTrigger
                                            trigger='click'
                                            key='sell-stop-loss-max-loss-percentage-overlay'
                                            placement='bottom'
                                            overlay={
                                              <Popover id='sell-stop-loss-max-loss-percentage-overlay-right'>
                                                <Popover.Content>
                                                  Set maximum loss percentage
                                                  for stop-loss. i.e. if set{' '}
                                                  <code>0.80</code>, it means
                                                  you won't lose than{' '}
                                                  <code>-20%</code> of the last
                                                  buy price. When you purchased
                                                  the coin at <code>$100</code>,
                                                  the last price will be set as{' '}
                                                  <code>$100</code>. And then
                                                  when the current price reaches{' '}
                                                  <code>$80</code>, the bot will
                                                  place the{' '}
                                                  <strong>market order</strong>{' '}
                                                  to sell all available balance.
                                                </Popover.Content>
                                              </Popover>
                                            }>
                                            <Button
                                              variant='link'
                                              className='p-0 m-0 ml-1 text-info'>
                                              <i className='fas fa-question-circle fa-sm'></i>
                                            </Button>
                                          </OverlayTrigger>
                                        </Form.Label>
                                        <Form.Control
                                          size='sm'
                                          type='number'
                                          placeholder='Enter maximum loss percentage'
                                          required
                                          max='1'
                                          min='0'
                                          step='0.0001'
                                          data-state-key='sell.stopLoss.maxLossPercentage'
                                          value={
                                            configuration.sell.stopLoss
                                              .maxLossPercentage
                                          }
                                          onChange={this.handleInputChange}
                                        />
                                      </Form.Group>
                                    </div>
                                    <div className='col-xs-12 col-sm-6'>
                                      <Form.Group
                                        controlId='field-sell-stop-loss-disable-buy-minutes'
                                        className='mb-2'>
                                        <Form.Label className='mb-0'>
                                          Temporary disable for buying (minutes){' '}
                                          <OverlayTrigger
                                            trigger='click'
                                            key='sell-stop-loss-disable-buy-minutes-overlay'
                                            placement='bottom'
                                            overlay={
                                              <Popover id='sell-stop-loss-disable-buy-minutes-overlay-right'>
                                                <Popover.Content>
                                                  Set for how long to disable
                                                  buying in minutes after
                                                  placing a stop-loss order.
                                                  i.e. if set <code>360</code>,
                                                  the bot will temporarily
                                                  disable buying for 6 hours.
                                                </Popover.Content>
                                              </Popover>
                                            }>
                                            <Button
                                              variant='link'
                                              className='p-0 m-0 ml-1 text-info'>
                                              <i className='fas fa-question-circle fa-sm'></i>
                                            </Button>
                                          </OverlayTrigger>
                                        </Form.Label>
                                        <Form.Control
                                          size='sm'
                                          type='number'
                                          placeholder='Enter minutes for disabling buy'
                                          required
                                          max='99999999'
                                          min='1'
                                          step='1'
                                          data-state-key='sell.stopLoss.disableBuyMinutes'
                                          value={
                                            configuration.sell.stopLoss
                                              .disableBuyMinutes
                                          }
                                          onChange={this.handleInputChange}
                                        />
                                      </Form.Group>
                                    </div>
                                  </div>
                                </Card.Body>
                              </Accordion.Collapse>
                            </Card>
                          </Accordion>
                        </div>
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </Card>
              </Accordion>

              <SettingIconTradingView
                botOptions={configuration.botOptions}
                tradingViewIntervals={tradingViewIntervals}
                handleBotOptionsChange={this.handleBotOptionsChange}
              />

              <Accordion defaultActiveKey='0'>
                <Card className='mt-1'>
                  <Card.Header className='px-2 py-1'>
                    <Accordion.Toggle
                      as={Button}
                      variant='link'
                      eventKey='0'
                      className='p-0 fs-7 text-uppercase'>
                      Conservative mode
                    </Accordion.Toggle>
                  </Card.Header>
                  <Accordion.Collapse eventKey='0'>
                    <Card.Body className='px-2 py-1'>
                      <div className='row'>
                        <div className='col-12'>
                          <Form.Group
                            controlId='field-sell-conservative-enabled'
                            className='mb-2'>
                            <Form.Check size='sm'>
                              <Form.Check.Input
                                type='checkbox'
                                data-state-key='sell.conservativeMode.enabled'
                                checked={
                                  configuration.sell.conservativeMode.enabled
                                }
                                onChange={this.handleInputChange}
                              />
                              <Form.Check.Label>
                                Reduce the sell trigger price proportionally to
                                the number of executed buy grids - applies only
                                to grids with at least 2 executed buy trades{' '}
                                <OverlayTrigger
                                  trigger='click'
                                  key='sell-conservative-enabled-overlay'
                                  placement='bottom'
                                  overlay={
                                    <Popover id='sell-conservative-enabled-overlay-right'>
                                      <Popover.Content>
                                        If enabled, the bot will sell at a
                                        trigger price reduced by the
                                        conservative ratio for each executed buy
                                        grid. You can use this feature in bear
                                        market conditions to secure smaller
                                        benefits over unreached higher gains. At
                                        least 2 buy trades must have been
                                        executed for the ratio to be applied.
                                      </Popover.Content>
                                    </Popover>
                                  }>
                                  <Button
                                    variant='link'
                                    className='p-0 m-0 ml-1 text-info'>
                                    <i className='fas fa-question-circle fa-sm'></i>
                                  </Button>
                                </OverlayTrigger>
                              </Form.Check.Label>
                            </Form.Check>
                          </Form.Group>
                        </div>
                        <div className='col-xs-12 col-sm-6'>
                          <Form.Group
                            controlId='field-sell-conservative-factor'
                            className='mb-2'>
                            <Form.Label className='mb-0'>
                              Conservative ratio{' '}
                              <OverlayTrigger
                                trigger='click'
                                key='sell-conservative-factor-overlay'
                                placement='bottom'
                                overlay={
                                  <Popover id='sell-conservative-factor-overlay-right'>
                                    <Popover.Content>
                                      Set the conservative factor to be applied
                                      on sell trades with at least 2 executed
                                      buy grids. i.e. if set to{' '}
                                      <code>0.90</code>, your current grid sell
                                      percentage will be reduced by{' '}
                                      <code>10%</code> for each executed buy
                                      grid (except the first one). For example,
                                      if your sell trigger percentage is{' '}
                                      <code>1.10</code>, and you have 3 executed
                                      buy grids, the sell order trigger will be{' '}
                                      <code>1.081</code>. Remember the sell
                                      trigger is not modified if you have only 1
                                      executed buy grid.
                                    </Popover.Content>
                                  </Popover>
                                }>
                                <Button
                                  variant='link'
                                  className='p-0 m-0 ml-1 text-info'>
                                  <i className='fas fa-question-circle fa-sm'></i>
                                </Button>
                              </OverlayTrigger>
                            </Form.Label>
                            <Form.Control
                              size='sm'
                              type='number'
                              placeholder='Enter conservative factor'
                              required
                              max='1'
                              min='0'
                              step='0.01'
                              data-state-key='sell.conservativeMode.factor'
                              value={configuration.sell.conservativeMode.factor}
                              onChange={this.handleInputChange}
                            />
                          </Form.Group>
                        </div>
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                </Card>
              </Accordion>

              <SettingIconBotOptions
                botOptions={configuration.botOptions}
                handleBotOptionsChange={this.handleBotOptionsChange}
              />

              <SettingIconActions />
            </Modal.Body>
            <Modal.Footer>
              <div className='w-100'>
                Note that the changes will display after the new price change is
                processed.
              </div>
              <Button
                variant='secondary'
                size='sm'
                onClick={() => this.handleModalClose('setting')}>
                Close
              </Button>
              <Button
                variant='primary'
                size='sm'
                disabled={!isValid}
                onClick={() => this.handleModalShow('confirm')}>
                Save Changes
              </Button>
            </Modal.Footer>
          </Form>
        </Modal>

        <Modal
          show={this.state.showConfirmModal}
          onHide={() => this.handleModalClose('confirm')}
          size='md'>
          <Modal.Header className='pt-1 pb-1'>
            <Modal.Title>
              <span className='text-danger'>⚠ Save Changes</span>
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            Warning: You are about to save the global configuration.
            <br />
            <br />
            Do you want to apply the changes for all symbols or just global
            configuration?
            <br />
            <br />
            If you choose to apply for all symbols, then customised symbol
            configurations will be removed.
            <br />
            <br />
            If you choose to apply the global configuration only, then the
            symbols that are different from the global configuration will be
            displayed as customised.
          </Modal.Body>

          <Modal.Footer>
            <Button
              variant='secondary'
              size='sm'
              onClick={() => this.handleModalClose('confirm')}>
              Cancel
            </Button>
            <Button
              variant='success'
              size='sm'
              onClick={() => this.handleFormSubmit({ action: 'apply-to-all' })}>
              Apply to all symbols
            </Button>
            <Button
              variant='primary'
              size='sm'
              onClick={() =>
                this.handleFormSubmit({
                  action: 'apply-to-global-only'
                })
              }>
              Apply to global only
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    );
  }
}
