{"editor.insertSpaces": true, "editor.formatOnSave": true, "editor.tabSize": 2, "files.trimTrailingWhitespace": true, "files.exclude": {}, "files.insertFinalNewline": true, "typescript.preferences.importModuleSpecifier": "relative", "search.exclude": {".git": true, ".build": true, "**/.DS_Store": true, "**/.vscode": true, "**/coverage": true, "**/node_modules": true}, "cSpell.words": ["bbands", "<PERSON><PERSON><PERSON>", "buildkitd", "buildx", "CERTDIR", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cummulative", "dind", "hgetall", "interruptible", "MACD", "mrkdwn", "redlock", "tradingview", "tulind", "uuidv"], "eslint.format.enable": true, "eslint.codeActionsOnSave.mode": "all"}