/* eslint-disable global-require */
describe('place-manual-trade.js', () => {
  let result;
  let error;
  let rawData;

  let binanceMock;
  let slackMock;
  let loggerMock;

  let mockGetAccountInfoFromAPI;
  let mockGetAPILimit;
  let mockGetAndCacheOpenOrdersForSymbol;

  let mockSaveManualOrder;

  beforeEach(() => {
    jest.clearAllMocks().resetModules();

    // Mock moment to return static date
    jest.mock(
      'moment',
      () => () => jest.requireActual('moment')('2020-01-01T00:00:00.000Z')
    );

    const { binance, slack, logger } = require('../../../../helpers');

    binanceMock = binance;
    slackMock = slack;
    loggerMock = logger;

    slackMock.sendMessage = jest.fn().mockResolvedValue(true);
    binanceMock.client.order = jest.fn().mockResolvedValue(true);

    mockGetAccountInfoFromAPI = jest.fn().mockResolvedValue({
      account: 'info'
    });

    mockGetAndCacheOpenOrdersForSymbol = jest.fn().mockResolvedValue([]);

    mockGetAPILimit = jest.fn().mockResolvedValue(10);
    mockSaveManualOrder = jest.fn().mockResolvedValue(true);
  });

  describe('when action is not manual-trade', () => {
    beforeEach(async () => {
      jest.mock('../../../trailingTradeHelper/common', () => ({
        getAccountInfoFromAPI: mockGetAccountInfoFromAPI,
        getAPILimit: mockGetAPILimit,
        getAndCacheOpenOrdersForSymbol: mockGetAndCacheOpenOrdersForSymbol
      }));

      jest.mock('../../../trailingTradeHelper/order', () => ({
        saveManualOrder: mockSaveManualOrder
      }));

      const step = require('../place-manual-trade');

      rawData = {
        symbol: 'BTCUSDT',
        action: 'buy-order-wait',
        symbolConfiguration: {
          system: {
            checkManualOrderPeriod: 10
          }
        },
        order: {}
      };

      result = await step.execute(loggerMock, rawData);
    });

    it('does not trigger getAndCacheOpenOrdersForSymbol', () => {
      expect(mockGetAndCacheOpenOrdersForSymbol).not.toHaveBeenCalled();
    });

    it('does not trigger saveManualOrder', () => {
      expect(mockSaveManualOrder).not.toHaveBeenCalled();
    });

    it('returns expected result', () => {
      expect(result).toStrictEqual({
        symbol: 'BTCUSDT',
        action: 'buy-order-wait',
        symbolConfiguration: {
          system: {
            checkManualOrderPeriod: 10
          }
        },
        order: {}
      });
    });
  });

  [
    {
      desc: 'BTCUSDT Buy Limit',
      order: {
        side: 'buy',
        buy: {
          type: 'limit',
          price: 39330.29,
          quantity: 0.1,
          total: 3933.029,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 0,
          isValid: true
        },
        sell: {
          type: 'limit',
          price: 39330.29,
          quantity: 0,
          total: 0,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 0,
          isValid: false
        }
      },
      expectedOrderParams: {
        symbol: 'BTCUSDT',
        side: 'buy',
        type: 'LIMIT',
        quantity: 0.1,
        price: 39330.29
      },
      orderResult: {
        symbol: 'BTCUSDT',
        orderId: 250376,
        orderListId: -1,
        clientOrderId: 'X7yz5s4UoDtkEW3j3oDDhe',
        transactTime: *************,
        price: '39330.********',
        origQty: '0.********',
        executedQty: '0.********',
        cummulativeQuoteQty: '0.********',
        status: 'NEW',
        timeInForce: 'GTC',
        type: 'LIMIT',
        side: 'BUY',
        fills: []
      },
      openOrders: [
        {
          symbol: 'BTCUSDT',
          orderId: 250376,
          orderListId: -1,
          clientOrderId: 'X7yz5s4UoDtkEW3j3oDDhe',
          price: '39330.********',
          origQty: '0.********',
          executedQty: '0.********',
          cummulativeQuoteQty: '0.********',
          status: 'NEW',
          timeInForce: 'GTC',
          type: 'LIMIT',
          side: 'BUY',
          stopPrice: '0.********',
          icebergQty: '0.********',
          time: *************,
          updateTime: *************,
          isWorking: true,
          origQuoteOrderQty: '0.********'
        }
      ],
      expectedData: {
        symbol: 'BTCUSDT',
        action: 'manual-trade',
        symbolConfiguration: { system: { checkManualOrderPeriod: 10 } },
        buy: {
          openOrders: [
            {
              symbol: 'BTCUSDT',
              orderId: 250376,
              orderListId: -1,
              clientOrderId: 'X7yz5s4UoDtkEW3j3oDDhe',
              price: '39330.********',
              origQty: '0.********',
              executedQty: '0.********',
              cummulativeQuoteQty: '0.********',
              status: 'NEW',
              timeInForce: 'GTC',
              type: 'LIMIT',
              side: 'BUY',
              stopPrice: '0.********',
              icebergQty: '0.********',
              time: *************,
              updateTime: *************,
              isWorking: true,
              origQuoteOrderQty: '0.********'
            }
          ],
          processMessage: 'Placed new manual order.',
          updatedAt: expect.any(Object)
        },
        sell: { openOrders: [] },
        order: {
          side: 'buy',
          buy: {
            type: 'limit',
            price: 39330.29,
            quantity: 0.1,
            total: 3933.029,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 0,
            isValid: true
          },
          sell: {
            type: 'limit',
            price: 39330.29,
            quantity: 0,
            total: 0,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 0,
            isValid: false
          }
        },
        openOrders: [
          {
            symbol: 'BTCUSDT',
            orderId: 250376,
            orderListId: -1,
            clientOrderId: 'X7yz5s4UoDtkEW3j3oDDhe',
            price: '39330.********',
            origQty: '0.********',
            executedQty: '0.********',
            cummulativeQuoteQty: '0.********',
            status: 'NEW',
            timeInForce: 'GTC',
            type: 'LIMIT',
            side: 'BUY',
            stopPrice: '0.********',
            icebergQty: '0.********',
            time: *************,
            updateTime: *************,
            isWorking: true,
            origQuoteOrderQty: '0.********'
          }
        ],
        accountInfo: { account: 'info' }
      }
    },
    {
      desc: 'BTCUSDT Buy Market Total',
      order: {
        side: 'buy',
        buy: {
          type: 'market',
          price: 39372.07,
          quantity: 0,
          total: 0,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 100,
          isValid: true
        },
        sell: {
          type: 'limit',
          price: 39372.07,
          quantity: 0,
          total: 0,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 0,
          isValid: false
        }
      },
      expectedOrderParams: {
        symbol: 'BTCUSDT',
        side: 'buy',
        type: 'MARKET',
        quoteOrderQty: 100
      },
      orderResult: {
        symbol: 'BTCUSDT',
        orderId: 251934,
        orderListId: -1,
        clientOrderId: 'Ejpj93cueEiwAuCoQ5vPmL',
        transactTime: *************,
        price: '0.********',
        origQty: '0.********',
        executedQty: '0.********',
        cummulativeQuoteQty: '99.99146880',
        status: 'FILLED',
        timeInForce: 'GTC',
        type: 'MARKET',
        side: 'BUY',
        fills: [
          {
            price: '39366.72000000',
            qty: '0.********',
            commission: '0.********',
            commissionAsset: 'BTC',
            tradeId: 68553
          }
        ]
      },
      openOrders: [],
      expectedData: {
        symbol: 'BTCUSDT',
        action: 'manual-trade',
        symbolConfiguration: { system: { checkManualOrderPeriod: 10 } },
        buy: {
          openOrders: [],
          processMessage: 'Placed new manual order.',
          updatedAt: expect.any(Object)
        },
        sell: { openOrders: [] },
        order: {
          side: 'buy',
          buy: {
            type: 'market',
            price: 39372.07,
            quantity: 0,
            total: 0,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 100,
            isValid: true
          },
          sell: {
            type: 'limit',
            price: 39372.07,
            quantity: 0,
            total: 0,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 0,
            isValid: false
          }
        },
        openOrders: [],
        accountInfo: { account: 'info' }
      }
    },
    {
      desc: 'BTCUSDT Buy Market Amount',
      order: {
        side: 'buy',
        buy: {
          type: 'market',
          price: 39365,
          quantity: 0,
          total: 0,
          marketType: 'amount',
          marketQuantity: 0.1,
          quoteOrderQty: 0,
          isValid: true
        },
        sell: {
          type: 'limit',
          price: 39365,
          quantity: 0,
          total: 0,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 0,
          isValid: false
        }
      },
      expectedOrderParams: {
        symbol: 'BTCUSDT',
        side: 'buy',
        type: 'MARKET',
        quantity: 0.1
      },
      orderResult: {
        symbol: 'BTCUSDT',
        orderId: 252348,
        orderListId: -1,
        clientOrderId: 'J537Cni9axtnhpPIZqhOWC',
        transactTime: *************,
        price: '0.********',
        origQty: '0.********',
        executedQty: '0.********',
        cummulativeQuoteQty: '3947.22326758',
        status: 'FILLED',
        timeInForce: 'GTC',
        type: 'MARKET',
        side: 'BUY',
        fills: [
          {
            price: '39432.26000000',
            qty: '0.01354900',
            commission: '0.********',
            commissionAsset: 'BTC',
            tradeId: 68645
          }
        ]
      },
      openOrders: [],
      expectedData: {
        symbol: 'BTCUSDT',
        action: 'manual-trade',
        symbolConfiguration: { system: { checkManualOrderPeriod: 10 } },
        buy: {
          openOrders: [],
          processMessage: 'Placed new manual order.',
          updatedAt: expect.any(Object)
        },
        sell: { openOrders: [] },
        order: {
          side: 'buy',
          buy: {
            type: 'market',
            price: 39365,
            quantity: 0,
            total: 0,
            marketType: 'amount',
            marketQuantity: 0.1,
            quoteOrderQty: 0,
            isValid: true
          },
          sell: {
            type: 'limit',
            price: 39365,
            quantity: 0,
            total: 0,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 0,
            isValid: false
          }
        },
        openOrders: [],
        accountInfo: { account: 'info' }
      }
    },
    {
      desc: 'BTCUSDT Sell Limit',
      order: {
        side: 'sell',
        buy: {
          type: 'limit',
          price: 39321.23,
          quantity: 0,
          total: 0,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 0,
          isValid: false
        },
        sell: {
          type: 'limit',
          price: 39321.23,
          quantity: 0.1,
          total: 3932.123,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 0,
          isValid: true
        }
      },
      expectedOrderParams: {
        symbol: 'BTCUSDT',
        side: 'sell',
        type: 'LIMIT',
        quantity: 0.1,
        price: 39321.23
      },
      orderResult: {
        symbol: 'BTCUSDT',
        orderId: 252849,
        orderListId: -1,
        clientOrderId: 'TAdKp3tT6Cloxyi0jaCKiJ',
        transactTime: *************,
        price: '39321.********',
        origQty: '0.********',
        executedQty: '0.********',
        cummulativeQuoteQty: '0.********',
        status: 'NEW',
        timeInForce: 'GTC',
        type: 'LIMIT',
        side: 'SELL',
        fills: []
      },
      openOrders: [],
      expectedData: {
        symbol: 'BTCUSDT',
        action: 'manual-trade',
        symbolConfiguration: { system: { checkManualOrderPeriod: 10 } },
        buy: {
          openOrders: [],
          processMessage: 'Placed new manual order.',
          updatedAt: expect.any(Object)
        },
        sell: { openOrders: [] },
        order: {
          side: 'sell',
          buy: {
            type: 'limit',
            price: 39321.23,
            quantity: 0,
            total: 0,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 0,
            isValid: false
          },
          sell: {
            type: 'limit',
            price: 39321.23,
            quantity: 0.1,
            total: 3932.123,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 0,
            isValid: true
          }
        },
        openOrders: [],
        accountInfo: { account: 'info' }
      }
    },
    {
      desc: 'BTCUSDT Sell Market Total',
      order: {
        side: 'sell',
        buy: {
          type: 'limit',
          price: 39284.99,
          quantity: 0,
          total: 0,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 0,
          isValid: false
        },
        sell: {
          type: 'market',
          price: 39284.99,
          quantity: 0,
          total: 0,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 100,
          isValid: true
        }
      },
      expectedOrderParams: {
        symbol: 'BTCUSDT',
        side: 'sell',
        type: 'MARKET',
        quoteOrderQty: 100
      },
      orderResult: {
        symbol: 'BTCUSDT',
        orderId: 254445,
        orderListId: -1,
        clientOrderId: 'g9NWI84xD4wwLe00f0VtlJ',
        transactTime: *************,
        price: '0.********',
        origQty: '0.********',
        executedQty: '0.********',
        cummulativeQuoteQty: '99.99684876',
        status: 'FILLED',
        timeInForce: 'GTC',
        type: 'MARKET',
        side: 'SELL',
        fills: [
          {
            price: '39276.06000000',
            qty: '0.********',
            commission: '0.********',
            commissionAsset: 'USDT',
            tradeId: 69077
          }
        ]
      },
      openOrders: [],
      expectedData: {
        symbol: 'BTCUSDT',
        action: 'manual-trade',
        symbolConfiguration: { system: { checkManualOrderPeriod: 10 } },
        buy: {
          openOrders: [],
          processMessage: 'Placed new manual order.',
          updatedAt: expect.any(Object)
        },
        sell: { openOrders: [] },
        order: {
          side: 'sell',
          buy: {
            type: 'limit',
            price: 39284.99,
            quantity: 0,
            total: 0,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 0,
            isValid: false
          },
          sell: {
            type: 'market',
            price: 39284.99,
            quantity: 0,
            total: 0,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 100,
            isValid: true
          }
        },
        openOrders: [],
        accountInfo: { account: 'info' }
      }
    },
    {
      desc: 'BTCUSDT Sell Market Amount',
      order: {
        side: 'sell',
        buy: {
          type: 'limit',
          price: 39168.94,
          quantity: 0,
          total: 0,
          marketType: 'total',
          marketQuantity: 0,
          quoteOrderQty: 0,
          isValid: false
        },
        sell: {
          type: 'market',
          price: 39168.94,
          quantity: 0,
          total: 0,
          marketType: 'amount',
          marketQuantity: 0.1,
          quoteOrderQty: 0,
          isValid: true
        }
      },
      expectedOrderParams: {
        symbol: 'BTCUSDT',
        side: 'sell',
        type: 'MARKET',
        quantity: 0.1
      },
      orderResult: {
        symbol: 'BTCUSDT',
        orderId: 254603,
        orderListId: -1,
        clientOrderId: 'jtWFk4FoIcGwk8aeP1hwnz',
        transactTime: *************,
        price: '0.********',
        origQty: '0.********',
        executedQty: '0.09569200',
        cummulativeQuoteQty: '3452.34603593',
        status: 'EXPIRED',
        timeInForce: 'GTC',
        type: 'MARKET',
        side: 'SELL',
        fills: [
          {
            price: '39114.25000000',
            qty: '0.01398500',
            commission: '0.********',
            commissionAsset: 'USDT',
            tradeId: 69148
          }
        ]
      },
      openOrders: null,
      expectedData: {
        symbol: 'BTCUSDT',
        action: 'manual-trade',
        symbolConfiguration: { system: { checkManualOrderPeriod: 10 } },
        buy: {
          openOrders: [],
          processMessage: 'Placed new manual order.',
          updatedAt: expect.any(Object)
        },
        sell: { openOrders: [] },
        order: {
          side: 'sell',
          buy: {
            type: 'limit',
            price: 39168.94,
            quantity: 0,
            total: 0,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 0,
            isValid: false
          },
          sell: {
            type: 'market',
            price: 39168.94,
            quantity: 0,
            total: 0,
            marketType: 'amount',
            marketQuantity: 0.1,
            quoteOrderQty: 0,
            isValid: true
          }
        },
        openOrders: [],
        accountInfo: { account: 'info' }
      }
    }
  ].forEach(testData => {
    describe(`${testData.desc}`, () => {
      beforeEach(async () => {
        mockGetAndCacheOpenOrdersForSymbol = jest
          .fn()
          .mockResolvedValue(
            testData.openOrders !== null ? testData.openOrders : []
          );

        jest.mock('../../../trailingTradeHelper/common', () => ({
          getAccountInfoFromAPI: mockGetAccountInfoFromAPI,
          getAPILimit: mockGetAPILimit,
          getAndCacheOpenOrdersForSymbol: mockGetAndCacheOpenOrdersForSymbol
        }));

        jest.mock('../../../trailingTradeHelper/order', () => ({
          saveManualOrder: mockSaveManualOrder
        }));

        binanceMock.client.order = jest
          .fn()
          .mockResolvedValue(testData.orderResult);

        const step = require('../place-manual-trade');

        rawData = {
          symbol: 'BTCUSDT',
          action: 'manual-trade',
          symbolConfiguration: {
            system: {
              checkManualOrderPeriod: 10
            }
          },
          buy: {
            openOrders: []
          },
          sell: {
            openOrders: []
          },
          order: testData.order
        };

        result = await step.execute(loggerMock, rawData);
      });

      it('triggers binance.client.order', () => {
        expect(binanceMock.client.order).toHaveBeenCalledWith(
          testData.expectedOrderParams
        );
      });

      it('triggers saveManualOrder', () => {
        expect(mockSaveManualOrder).toHaveBeenCalledWith(
          loggerMock,
          'BTCUSDT',
          testData.orderResult.orderId,
          {
            ...testData.orderResult
          }
        );
      });

      it('returns expected result', () => {
        expect(result).toStrictEqual(testData.expectedData);
      });
    });
  });

  describe('when unknown order side/type is provided', () => {
    beforeEach(async () => {
      jest.mock('../../../trailingTradeHelper/common', () => ({
        getAccountInfoFromAPI: mockGetAccountInfoFromAPI,
        getAPILimit: mockGetAPILimit,
        getAndCacheOpenOrdersForSymbol: mockGetAndCacheOpenOrdersForSymbol
      }));

      jest.mock('../../../trailingTradeHelper/order', () => ({
        saveManualOrder: mockSaveManualOrder
      }));

      binanceMock.client.order = jest.fn().mockResolvedValue(true);

      const step = require('../place-manual-trade');

      rawData = {
        symbol: 'BTCUSDT',
        action: 'manual-trade',
        symbolConfiguration: {
          system: {
            checkManualOrderPeriod: 10
          }
        },
        buy: {
          openOrders: []
        },
        sell: {
          openOrders: []
        },
        order: {
          side: 'sell-not-valid',
          buy: {
            type: 'limit',
            price: 39168.94,
            quantity: 0,
            total: 0,
            marketType: 'total',
            marketQuantity: 0,
            quoteOrderQty: 0,
            isValid: false
          },
          sell: {
            type: 'market',
            price: 39168.94,
            quantity: 0,
            total: 0,
            marketType: 'amount',
            marketQuantity: 0.1,
            quoteOrderQty: 0,
            isValid: true
          }
        }
      };

      try {
        result = await step.execute(loggerMock, rawData);
      } catch (e) {
        error = e;
      }
    });

    it('throws exception', () => {
      expect(error).toStrictEqual(
        new Error('Unknown order side/type for manual trade')
      );
    });
  });
});
