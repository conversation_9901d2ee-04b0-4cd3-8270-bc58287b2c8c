name: Release & Docker

on:
  repository_dispatch:
    types: [Release & Docker]
  workflow_dispatch:

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    steps:
      - name: Create Release
        id: create_release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ github.event.client_payload.ref }}
          name:
            '<Update release name for ${{ github.event.client_payload.ref }}>'
          draft: true
          prerelease: false
          generate_release_notes: true

  docker-images:
    name: Build/Push production docker image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.client_payload.ref }}

      # https://github.com/docker/setup-qemu-action#usage
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      # https://github.com/marketplace/actions/docker-setup-buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      # https://github.com/docker/login-action#docker-hub
      - name: Login to <PERSON>er Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Get build args
        run: |
          echo "PACKAGE_VERSION=$(grep -m1 version package.json | cut -c 15- | rev | cut -c 3- | rev)" >> $GITHUB_ENV
          echo "GIT_HASH=$(git rev-parse --short HEAD)" >> $GITHUB_ENV

      # https://github.com/docker/build-push-action#multi-platform-image
      - name: Build/Push production docker image
        uses: docker/build-push-action@v2
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm/v7,linux/arm64
          push: true
          target: production-stage
          build-args: |
            PACKAGE_VERSION=${{ env.PACKAGE_VERSION }}
            GIT_HASH=${{ env.GIT_HASH }}
            NODE_ENV=production
          tags: |
            chrisleekr/binance-trading-bot:latest
            chrisleekr/binance-trading-bot:${{ env.PACKAGE_VERSION }}

      # https://github.com/docker/build-push-action#multi-platform-image
      - name: Build/Push production docker image - tradingview
        uses: docker/build-push-action@v2
        with:
          context: ./tradingview
          file: ./tradingview/Dockerfile
          platforms: linux/amd64,linux/arm/v7,linux/arm64
          push: true
          tags: |
            chrisleekr/binance-trading-bot:tradingview
            chrisleekr/binance-trading-bot:tradingview-${{ env.PACKAGE_VERSION }}
