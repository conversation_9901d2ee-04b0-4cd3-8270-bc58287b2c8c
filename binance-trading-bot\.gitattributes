# Set the default behavior, in case people don't have core.autocrlf set.
* text eol=lf

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.php text eol=lf
*.phps text eol=lf
*.inc text eol=lf
*.js text eol=lf
*.css text eol=lf
*.ini text eol=lf
*.json text eol=lf
*.htm text eol=lf
*.html text eol=lf
*.xml text eol=lf
*.xslt text eol=lf
*.svg text eol=lf
*.txt text eol=lf
*.md text eol=lf
*.sh text eol=lf
CHANGELOG text eol=lf
README text eol=lf
RELEASENOTES text eol=lf

# Declare files that will always have CRLF line endings on checkout.
*.sln text eol=crlf
*.eot text eol=crlf
*.ttf text eol=crlf


# Denote all files that are truly binary and should not be modified.
*.acorn binary
*.png binary
*.jpg binary
*.jpeg binary
*.z binary
*.gif binary
*.jpa binary
*.jps binary
*.zip binary
*.dll binary
*.exe binary
*.jar binary
*.phar binary
*.eot binary
*.otf binary
*.ttf binary
*.woff binary
*.woff2 binary
