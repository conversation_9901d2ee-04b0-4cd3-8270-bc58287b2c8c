name: Push

on: [push, workflow_dispatch]

concurrency:
  group: build-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  lint-and-test:
    name: Lint & <PERSON>
    if: "!contains(github.event.head_commit.message, 'skip ci')"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source code
        uses: actions/checkout@v2
      - uses: actions/setup-node@v4
        with:
          node-version: 14
      - name: Testing
        run: |
          npm install
          npm run lint
          npm run test
      - name: Uploading test coverage
        uses: codecov/codecov-action@v3

  docker-images:
    name: Build/Push development docker image
    if:
      github.repository == 'chrisleekr/binance-trading-bot' && github.ref ==
      'refs/heads/master'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      # https://github.com/docker/setup-qemu-action#usage
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      # https://github.com/marketplace/actions/docker-setup-buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      # https://github.com/docker/login-action#docker-hub
      - name: Login to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Get build args
        run: |
          echo "PACKAGE_VERSION=$(grep -m1 version package.json | cut -c 15- | rev | cut -c 3- | rev)" >> $GITHUB_ENV
          echo "GIT_HASH=$(git rev-parse --short HEAD)" >> $GITHUB_ENV

      # https://github.com/docker/build-push-action#multi-platform-image
      - name: Build/Push development docker image
        uses: docker/build-push-action@v2
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm/v7,linux/arm64
          push: true
          target: production-stage
          build-args: |
            PACKAGE_VERSION=${{ env.PACKAGE_VERSION }}
            GIT_HASH=${{ env.GIT_HASH }}
            NODE_ENV=production
          tags: |
            chrisleekr/binance-trading-bot:development

      # https://github.com/docker/build-push-action#multi-platform-image
      - name: Build/Push development docker image - tradingview
        uses: docker/build-push-action@v2
        with:
          context: ./tradingview
          file: ./tradingview/Dockerfile
          platforms: linux/amd64,linux/arm/v7,linux/arm64
          push: true
          tags: |
            chrisleekr/binance-trading-bot:tradingview-development
