/* eslint-disable global-require */
const { binance, logger } = require('../../../helpers');

const aliveHelper = require('../helper');

jest.mock('config');

describe('helper', () => {
  let result;

  describe('getAccountInfo', () => {
    beforeEach(async () => {
      binance.client.accountInfo = jest.fn().mockResolvedValue({
        updateTime: '*************',
        balances: [
          { asset: 'BTC', free: '0.********', locked: '0.********' },
          { asset: 'ETH', free: '0.********', locked: '0.********' }
        ]
      });

      result = await aliveHelper.getAccountInfo(logger);
    });

    it('return expected result', () => {
      expect(result).toStrictEqual({
        updateTime: '*************',
        balances: [{ asset: 'BTC', free: '0.********', locked: '0.********' }]
      });
    });
  });
});
