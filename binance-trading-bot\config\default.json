{"mode": "test", "tz": "Australia/Melbourne", "appName": "Binance Trading Bot", "demoMode": false, "frontend": {"port": 80}, "binance": {"live": {"apiKey": "", "secretKey": ""}, "test": {"apiKey": "", "secretKey": ""}}, "redis": {"host": "binance-redis", "port": 6379, "password": "", "db": 0}, "mongo": {"host": "binance-mongo", "port": 27017, "database": "binance-bot"}, "tradingview": {"host": "tradingview", "port": 8080}, "slack": {"enabled": false, "webhookUrl": "", "channel": "", "username": ""}, "localTunnel": {"enabled": true, "subdomain": "default"}, "featureToggle": {"notifyOrderConfirm": true, "notifyDebug": false, "notifyOrderExecute": true}, "authentication": {"enabled": true, "password": "123456", "loginLimiter": {"maxConsecutiveFails": 5, "duration": 10800, "blockDuration": 900}}, "jobs": {"alive": {"enabled": true, "cronTime": "0 0 9 * * *"}, "trailingTrade": {"enabled": true, "cronTime": "* * * * * *", "symbols": ["BTCUSDT", "ETHUSDT", "ETHBTC", "XRPBTC"], "botOptions": {"authentication": {"lockList": true, "lockAfter": 120}, "autoTriggerBuy": {"enabled": false, "triggerAfter": 20, "conditions": {"whenLessThanATHRestriction": true, "afterDisabledPeriod": true}}, "orderLimit": {"enabled": true, "maxBuyOpenOrders": 3, "maxOpenTrades": 5}, "tradingViews": [{"interval": "1h", "buy": {"whenStrongBuy": true, "whenBuy": true}, "sell": {"forceSellOverZeroBelowTriggerPrice": {"whenNeutral": false, "whenSell": false, "whenStrongSell": false}}}], "tradingViewOptions": {"useOnlyWithin": 5, "ifExpires": "ignore"}, "logs": {"deleteAfter": 30}}, "candles": {"interval": "1h", "limit": 100}, "buy": {"enabled": true, "gridTrade": [{"triggerPercentage": 1, "stopPercentage": 1.02, "limitPercentage": 1.021, "minPurchaseAmount": -1, "minPurchaseAmounts": {}, "maxPurchaseAmount": -1, "maxPurchaseAmounts": {}}], "lastBuyPriceRemoveThreshold": -1, "lastBuyPriceRemoveThresholds": {}, "athRestriction": {"enabled": true, "candles": {"interval": "1d", "limit": 30}, "restrictionPercentage": 0.9}}, "sell": {"enabled": true, "gridTrade": [{"triggerPercentage": 1.06, "stopPercentage": 0.98, "limitPercentage": 0.979, "quantityPercentage": -1, "quantityPercentages": {}}], "stopLoss": {"enabled": false, "maxLossPercentage": 0.8, "disableBuyMinutes": 360, "orderType": "market"}, "conservativeMode": {"enabled": false, "factor": 0.5}}, "system": {"temporaryDisableActionAfterConfirmingOrder": 20, "checkManualOrderPeriod": 5, "placeManualOrderInterval": 5, "refreshAccountInfoPeriod": 1, "checkOrderExecutePeriod": 10}}, "trailingTradeIndicator": {"enabled": true, "cronTime": "* * * * * *"}, "tradingView": {"enabled": true, "cronTime": "* * * * * *"}}}