/* Dark Mode styles */
html,
body {
  height: 100%;
}

body {
  overflow-y: scroll; /* Show vertical scrollbar */
}

body.dark-theme {
  color: #cfcfcf;
  background: #191f2c;
  font-size: 0.8rem;
}

.divider {
  border-top: 1px dashed #585858;
}

/* Disable scrolling */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type='number'] {
  -moz-appearance: textfield;
}

.accordion-body.in {
  overflow: visible;
}

.fs-1 {
  font-size: 2.5rem !important;
}

.fs-2 {
  font-size: 2rem !important;
}

.fs-3 {
  font-size: 1.75rem !important;
}

.fs-4 {
  font-size: 1.5rem !important;
}

.fs-5 {
  font-size: 1.25rem !important;
}

.fs-6 {
  font-size: 1rem !important;
}

.fs-7 {
  font-size: 0.8rem !important;
}

.fs-8 {
  font-size: 0.7rem !important;
}

.fs-9 {
  font-size: 0.6rem !important;
}

.w-10 {
  width: 10% !important;
}

.w-15 {
  width: 15% !important;
}

.w-20 {
  width: 20% !important;
}

.w-30 {
  width: 30% !important;
}

.w-row-3 {
  width: 33.33333% !important;
}

.w-40 {
  width: 40% !important;
}

.w-60 {
  width: 60% !important;
}

.w-70 {
  width: 70% !important;
}

.line-height-1 {
  line-height: 1 !important;
}

.modal-header .close {
  margin-top: -0.5rem;
}

.app {
  padding: 10px;
  height: 100%;
}

.app-body-loading {
  text-align: center;
}

.app-h1 {
  font-size: 1rem;
  width: 100%;
  text-transform: uppercase;
  font-weight: bold;
}

.app-h1 .binance-img {
  width: 17px;
  vertical-align: text-top;
}

.app-h1 i {
  color: #fff;
}

.app-header {
  margin-bottom: 5px;
}

.header-wrapper {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.header-wrapper .header-column.header-column-title {
  text-align: left;
  display: flex;
  flex-flow: row wrap;
}

.header-wrapper .header-column.header-column-title h1 {
  line-height: 25px;
}

.header-wrapper .header-column.header-column-icon {
  text-align: right;
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-end;
}

.header-wrapper .header-column.header-column-icon i {
  color: #fff;
  font-size: 20px;
}

.footer-wrapper {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
}

.footer-wrapper .footer-column {
  text-align: center;
  display: flex;
  flex-flow: row wrap;
}

.coin-info-label-with-icon {
  display: flex;
  justify-content: flex-start;
}

.coin-info-label-with-icon button {
  padding: 0;
  line-height: 15px;
  margin: 0 0 0 5px;
}
.coin-info-label-with-icon i {
  color: #fff;
  /* font-size: 15px; */
}

.coin-info-column-price .coin-info-value-with-icon {
  display: flex;
  justify-content: flex-end;
}

.coin-info-column-price .coin-info-value-with-icon button {
  padding: 0;
  line-height: 15px;
  margin: 0 0 0 5px;
}
.coin-info-column-price .coin-info-value-with-icon i {
  color: #fff;
  font-size: 15px;
}

.coin-info-column-rows {
  display: flex;
  flex-flow: column wrap;
}

.coin-info-column-row {
  display: flex;
  flex-flow: row wrap;
}

.coin-info-column-row .coin-info-column {
  flex: 1 1 auto;

  border: 1px solid rgba(86, 61, 124, 0.15);
  display: inline-block;
}

.accordion-wrapper {
  margin-bottom: 10px;
}

.account-balance-assets-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-gap: 0.5rem;
}

.account-wrapper-assets {
}

.account-wrapper-body {
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;

  padding: 10px;
  background-color: #272b38;
}

.accordion-wrapper .btn-account-balance,
.accordion-wrapper .btn-status {
  color: #809fff;
}

.accordion-wrapper .btn-profit-loss {
  display: inline-block;
  font-weight: 400;
  color: #809fff;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.account-balance-actions-wrapper .btn-convert-to-bnb {
  color: #f4bd34;
}

.account-asset-coin {
  font-weight: bold;
}

.account-asset-row {
  display: flex;
  flex-flow: row wrap;
}
.account-asset-row-valignfix {
  opacity: 0;
}

.account-asset-label {
  flex: 1 40%;
}
.account-asset-value {
  flex: 1 60%;
  text-align: right;
}

.coin-wrappers {
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.coin-wrapper {
  width: 8.33%;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  padding: 5px;
  margin-bottom: 10px;
  flex: 8.33% auto;
}

@media (max-width: 3300px) {
  .coin-wrapper {
    width: 16.66%;
    flex: 16.66% auto;
  }
}

@media (max-width: 1650px) {
  .coin-wrapper {
    width: 25%;
    flex: 25% auto;
  }
}

@media (max-width: 1100px) {
  .coin-wrapper {
    width: 33.33%;
    flex: 33.33% auto;
  }
}

@media (max-width: 825px) {
  .coin-wrapper {
    width: 50%;
    flex: 50% auto;
  }
}

@media (max-width: 550px) {
  .coin-wrapper {
    width: 100%;
    flex: 100% auto;
  }
}

.coin-wrapper-even {
  background-color: #272b38;
}

.coin-info-wrapper {
  width: 100%;
  padding: 10px;
  display: flex;
  flex-flow: row wrap;
}

.coin-info-sub-wrapper {
  width: 100%;
  display: flex;
  flex-flow: row wrap;
}

.coin-info-sub-open-order-wrapper {
  width: 100%;
  display: flex;
  flex-flow: row wrap;
}

.coin-info-column {
  flex: 1 100%;
  display: flex;
  flex-direction: row;
}

.coin-info-column-name {
  font-weight: bold;
  color: #fff;
  font-size: 1rem;
}

.coin-symbol {
  color: #809fff;
}

.coin-info-spinner {
  margin: 3px 5px 3px 3px;
}

.coin-info-sub-wrapper-symbol {
  display: flex;
  flex-flow: row wrap;
}

.coin-info-sub-wrapper-symbol .coin-info-column {
  flex: 1 auto;
  display: flex;
  flex-flow: row wrap;
}

.coin-info-sub-wrapper-symbol .coin-info-column-icon {
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-end;
  color: #fff;
}

.coin-info-sub-wrapper-symbol .coin-info-column-icon button {
  color: #fff;
}

.coin-info-column-title {
  border-top: 1px solid rgb(143, 143, 143);
  border-bottom: 1px solid rgb(143, 143, 143);
  margin: 5px 0 5px 0;
  padding: 5px 0 5px 0;
}

.coin-info-sub-open-order-wrapper:first-child .coin-info-column-title {
  border-top: none;
  margin-top: 0;
  padding-top: 0;
}

.coin-info-column-title-setting {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.coin-info-column-title-setting i {
  color: #fff;
  font-size: 15px;
}

.coin-info-content-setting {
  border-top: 1px solid rgb(143, 143, 143);
  width: 100%;
  padding-top: 5px;
  margin-top: 5px;
  background-color: #212529;
}

.coin-info-column-title-setting .coin-info-label {
  display: flex;
  justify-content: flex-start;
}

.coin-info-sub-label {
  display: block;
  width: 100%;
  font-weight: bold;
  border-bottom: 1px dashed #585858;
}

.coin-info-label,
.coin-info-value {
  flex: 1 auto;
  line-height: 23px;
}

.coin-info-column-tradingview .coin-info-label,
.coin-info-column-tradingview .coin-info-value {
  line-height: inherit;
}

.coin-info-label {
  width: auto;
}

.coin-info-value {
  width: auto;
}

.coin-info-column-title .coin-info-label {
  font-weight: bold;
}

.coin-info-column-title .coin-info-value {
  font-style: bold;
  font-size: 0.8rem;
}

.coin-info-value {
  text-align: right;
}

.coin-info-sub-open-order-wrapper .coin-info-column-title .coin-info-label {
  font-weight: normal;
}

.coin-info-value-up,
.coin-info-value-buy,
.buy-colour {
  color: #02c076;
}

.coin-info-value-down,
.coin-info-value-sell,
.sell-colour {
  color: #f84960;
}

.blink {
  animation: blinker 0.5s normal;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

.modal-dialog {
  color: #272b38;
}

.form-header {
  font-size: 0.8rem;
  text-transform: uppercase;
}

.fa-toggle-on {
  color: #02c076;
}

.fa-toggle-off {
  color: #f84960;
}

.coin-setting-customised {
  color: #deb887;
}

/**
  Start: Manual Trade
*/
.manual-trade .input-group > .input-group-prepend {
  flex: 0 0 25%;
}
.manual-trade .input-group .input-group-text {
  width: 100%;
}
.manual-trade .input-group > .input-group-append {
  flex: 0 0 25%;
}

.btn-manual {
  margin: 2px 0 0;
  appearance: none;
  user-select: none;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  box-sizing: border-box;
  text-align: center;
  text-decoration: none;
  outline: none;
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 20px;
  word-break: keep-all;
  padding: 6px 12px;
  height: 45px;
  border: none;
  border-radius: 4px;
  color: white;
}

.btn-manual:active,
.btn-manual:hover {
  color: white;
}

.btn-manual-trade {
  padding: 0 0.5rem;
  font-size: 0.8rem;
  line-height: 1.5;
  background-color: #6f42c1;
  color: #fff;
}

.btn-manual-trade:hover {
  background-color: rgba(110, 66, 193, 0.855);
  color: #fff;
  text-decoration: none;
}

.btn-trigger-grid-trade {
  padding: 0 0.5rem;
  font-size: 0.8rem;
  line-height: 1.5;
  background-color: rgba(40, 167, 69, 0.95);
  color: #fff;
}

.btn-trigger-grid-trade:hover {
  background-color: rgba(40, 167, 69, 0.85);
  color: #fff;
  text-decoration: none;
}

.btn-manual-buy {
  background-color: rgb(2, 192, 118);
}

.btn-manual-buy:active:not(:disabled) {
  box-shadow: none;
  background-color: rgb(21, 152, 101);
}

.btn-manual-buy:hover:not(:disabled):not(:active) {
  box-shadow: none;
  background-color: rgb(46, 209, 145);
}

.btn-manual-sell {
  background-color: rgb(248, 73, 96);
}

.btn-manual-sell:active:not(:disabled) {
  box-shadow: none;
  background-color: rgb(207, 48, 74);
}

.btn-manual-sell:hover:not(:disabled):not(:active) {
  box-shadow: none;
  background-color: rgb(255, 66, 101);
}

.manual-trade-wrappers {
  display: flex;
  flex-direction: row;
}

.manual-trade-wrapper {
  width: 50%;
  padding-right: 20px;
}

@media (max-width: 800px) {
  .manual-trade-wrappers {
    flex-direction: column;
  }

  .manual-trade-wrapper {
    width: 100%;
    padding-right: 0;
  }

  .manual-trade-sell-wrapper {
    margin-top: 20px;
  }
}

.manual-trade-all-wrapper {
  width: 100%;
  padding-right: 0;
}

.manual-trade-all-row-base-assets-wrapper {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;
}

.manual-trade-all-row-base-assets {
  width: 50%;
}

@media (max-width: 600px) {
  .manual-trade-all-row-base-assets-wrapper {
    flex-direction: column;
  }

  .manual-trade-all-row-base-assets {
    width: 100%;
  }
}

.manual-trade-title-buy {
  font-weight: bold;
  font-size: 1.2rem;
}

.manual-trade-quote-asset-title {
  margin-top: 10px;
  font-size: 0.9rem;
  border-bottom: 1px solid #c1c1c1;
}

.btn-percentage {
  font-size: 0.7rem !important;
  line-height: 1 !important;
  padding: 0.2rem 0.5rem !important;
}

/**
  End: Manual Trade
*/

.btn-cancel-order {
  line-height: 0.8rem;
  vertical-align: unset;
  color: #f84960;
}

.btn-cancel-order:hover {
  color: #f84960ce;
}

.btn-span {
  color: inherit;
  font-weight: inherit;
  background-color: inherit;
  font-size: inherit;
  border: inherit;
  border-radius: inherit;
  width: inherit;
  vertical-align: inherit;
  text-align: inherit;
  line-height: inherit;
  padding: 0;
}

.btn-span-disabled {
  pointer-events: none;
}

.btn-span:hover {
  color: inherit;
}

.btn-span:focus {
  outline: none;
  box-shadow: none;
}

/**
  Start: Profit Loss
*/
.profit-loss-container {
  display: flex;
  flex-direction: row;
}

.profit-loss-closed-trades-accordion-wrapper {
  margin-left: 5px;
}

.profit-loss-accordion-wrapper {
  width: 50%;
}

@media (max-width: 825px) {
  .profit-loss-container {
    flex-direction: column;
  }

  .profit-loss-accordion-wrapper {
    width: 100%;
  }

  .profit-loss-open-trades-accordion-wrapper {
    margin-right: 0;
  }

  .profit-loss-closed-trades-accordion-wrapper {
    margin-left: 0;
  }
}

.profit-loss-wrappers {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start !important;
}

.profit-loss-wrapper {
  width: 16.6%;
  min-width: 12rem;
}

.profit-loss-wrapper-body {
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  padding: 10px;
  background-color: #272b38;

  display: flex;
  flex-flow: row nowrap;
}

.profit-loss-asset {
  flex: 1 auto;
  font-weight: bold;
}

.profit-loss-value {
  flex: 1 auto;
  text-align: right;
  font-weight: bold;
}

@media (max-width: 3300px) {
  .profit-loss-wrapper {
    width: 33.3333%;
  }
}

@media (max-width: 1650px) {
  .profit-loss-wrapper {
    width: 50%;
  }
}

.btn-period {
  padding: 0 0.5rem;
  font-size: 0.8rem;
  line-height: 1.5;
}

/**
  End: Profit Loss
*/

/**
  Start: Order Stats
*/
.order-stats-wrapper {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;

  display: flex;
  flex-flow: row nowrap;
}

.order-stat-wrapper {
  margin-right: 10px;
}

.order-stat-label {
  font-weight: bold;
  margin-right: 3px;
}

.order-stat-value {
  background-color: #272b38;

  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;

  min-width: 15px;
  display: inline-block;
  text-align: center;

  padding: 0 4px 0 4px;
}
/**
  End: Order Stats
*/

/**
  Start: Dust Transfer
*/
.btn-dust-transfer {
  color: #c99400;
}

.btn-dust-transfer:hover {
  color: #e2a600;
}

.checkbox-dust-transfer-symbol label {
  line-height: 1.5rem;
}
/**
  End: Dust Transfer
*/

/**
  Start: Symbols selection
*/
.btn-clear-symbols {
  background-color: #f84960;
  color: white;
}

.btn-clear-symbols:hover {
  box-shadow: none;
  color: white;
  background-color: #ff4265;
}
/**
  End: Symbols selection
*/

/**
  Start: Grid Trade
*/
.btn-add-new-grid-trade-buy {
  background-color: #02c076;
  color: white;
}

.btn-add-new-grid-trade-buy:hover {
  box-shadow: none;
  color: white;
  background-color: #2ed191;
}

.btn-add-new-grid-trade-sell {
  background-color: #f84960;
  color: white;
}

.btn-add-new-grid-trade-sell:hover {
  box-shadow: none;
  color: white;
  background-color: #ff4265;
}

.coin-info-column-grid {
  flex: 1 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #444546;
  padding: 3px;
  margin-bottom: -1px;
}

.coin-info-column-grid:last-child {
  margin-bottom: 0;
}

.coin-info-grid-label {
  width: 100%;
}
/**
  End: Grid Trade
*/

/**
  Start: Filter
*/
.sort-wrapper .has-search-keyword {
  position: relative;
}

.sort-wrapper .has-search-keyword:after {
  content: '';
  background: red;
  border-radius: 50%;
  position: absolute;
  top: 6px;
  width: 4px;
  height: 4px;
}
/**
  End: Filter
*/

/**
  Start: Lock screen
*/
.lock-screen-wrapper {
  margin: 0 auto;
}
/**
  End: Lock screen
*/

/**
  Start: Trade Modal
*/
.trade-stats-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.trade-stat-wrapper {
  width: 25%;
}

@media (max-width: 500px) {
  .trade-stat-wrapper {
    width: 50%;
  }
}
/**
  End: Trade Modal
*/

.tradingview-widget-container iframe {
  overflow-y: hidden;
}

/**
  Start: Pagination
*/
.pagination {
  justify-content: center;
}

.pagination .page-link {
  color: #809fff;
  background-color: #343a40;
  border: 1px solid #2d3237;
}

.pagination .page-item.active .page-link {
  background-color: #6f42c1;
  border-color: #6f42c1;
}

.pagination .page-link:hover {
  color: #fff;
  background-color: rgba(110, 66, 193, 0.855);
  border-color: rgba(110, 66, 193, 0.855);
}
.pagination .page-item.disabled .page-link {
  background-color: #272b38;
  border-color: #343a40;
}

.pagination .page-item {
  min-width: 2rem;
  text-align: center;
}
/**
  End: Pagination
*/

/**
  Start: Animation
*/

.blink {
  animation: blinker 1s linear infinite;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

/**
  End: Animation
*/

/**
  Start: Dropzone
**/
.dropzone {
  text-align: center;
  padding: 30px;
  border: 3px dashed #eeeeee;
  background-color: #fafafa;
  color: #bdbdbd;
  cursor: pointer;
  margin-bottom: 20px;
}

.selected-file-wrapper {
  text-align: center;
}

.selected-file {
  color: #000;
  font-weight: bold;
}
/**
  End: Dropzone
**/

/**
  Start: TradingView
**/
.coin-info-tradingview-name {
  width: 40%;
}

.coin-info-tradingview-recommend {
  width: 10%;
}

.btn-add-new-tradingview {
  background-color: #02c076;
  color: white;
}

.btn-add-new-grid-trade-buy:hover {
  box-shadow: none;
  color: white;
  background-color: #2ed191;
}
/**
  End: TradingView
**/
