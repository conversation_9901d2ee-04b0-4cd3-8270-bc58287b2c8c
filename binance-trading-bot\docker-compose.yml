version: '3.7'

services:
  binance-bot:
    container_name: binance-bot
    build:
      context: ./
      target: dev-stage
      # target: production-stage
    networks:
      - internal
    volumes:
      - ./config:/srv/config
      - ./app:/srv/app
      - ./public:/srv/public
      - ./migrations:/srv/migrations
      - ./scripts:/srv/scripts
    env_file:
      - .env
    restart: unless-stopped
    environment:
      - BINANCE_MODE=test
      # - BINANCE_MODE=live
      - BINANCE_REDIS_HOST=binance-redis
      - BINANCE_REDIS_PORT=6379
      - BINANCE_REDIS_PASSWORD=secretp422
      - BINANCE_LOG_LEVEL=DEBUG
      - BINANCE_TRADINGVIEW_HOST=tradingview
      - BINANCE_TRADINGVIEW_PORT=8082
    ports:
      - 8080:80
    logging:
      driver: 'json-file'
      options:
        max-size: '1G'

  tradingview:
    container_name: tradingview
    build:
      context: ./tradingview
    networks:
      - internal
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
      # https://docs.python.org/3/howto/logging.html#logging-levels
      - BINANCE_TRADINGVIEW_LOG_LEVEL=INFO
      - BINANCE_TRADINGVIEW_PORT=8082
    ports:
      - 8082:8082
    logging:
      driver: 'json-file'
      options:
        max-size: '50m'

  binance-redis:
    container_name: binance-redis
    image: redis:6.2.4
    sysctls:
      net.core.somaxconn: 1024
    restart: unless-stopped
    networks:
      - internal
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command:
      redis-server /usr/local/etc/redis/redis.conf --requirepass secretp422

  binance-mongo:
    container_name: binance-mongo
    image: mongo:3.2.20-jessie
    restart: unless-stopped
    networks:
      - internal
    volumes:
      - mongo_data:/data/db

  binance-mongo-express:
    container_name: binance-mongo-express
    image: mongo-express:0.54
    restart: unless-stopped
    networks:
      - internal
    ports:
      - 8081:8081
    environment:
      ME_CONFIG_MONGODB_SERVER: binance-mongo

networks:
  internal:
    driver: bridge
    # driver_opts:
    #     com.docker.network.driver.mtu: 1442

volumes:
  redis_data:
  mongo_data:
