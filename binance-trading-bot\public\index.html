<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Binance Auto Trading Bot Dashboard</title>
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link
      rel="manifest"
      href="/site.webmanifest"
      crossorigin="use-credentials" />
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#191f2c" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Binance Trading Bot" />
    <meta name="application-name" content="Binance Trading Bot" />
    <meta name="msapplication-TileColor" content="#191f2c" />
    <meta name="theme-color" content="#191f2c" />
    <link
      rel="stylesheet"
      href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css"
      integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk"
      crossorigin="anonymous" />
    <link
      rel="stylesheet"
      href="./css/fontawesome/all.min.css"
      crossorigin="anonymous" />
    <link
      rel="stylesheet"
      href="https://unpkg.com/react-bootstrap-typeahead@5.1.4/css/Typeahead.min.css" />
    <link rel="stylesheet" href="https://unpkg.com/notyf@3.9.0/notyf.min.css" />

    <link rel="stylesheet" href="./css/App.css" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  </head>
  <body class="dark-theme dark-mode">
    <div id="app" class="app"></div>

    <!--
    <script
      src="https://unpkg.com/react@17/umd/react.production.min.js"
      crossorigin
    ></script>
    <script
      src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"
      crossorigin
    ></script>
    -->

    <script
      src="https://unpkg.com/react@17/umd/react.development.js"
      crossorigin></script>
    <script
      src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"
      crossorigin></script>

    <script
      src="https://unpkg.com/babel-standalone@6/babel.min.js"
      crossorigin></script>
    <script
      src="https://unpkg.com/react-bootstrap@1.5.2/dist/react-bootstrap.min.js"
      crossorigin></script>

    <script
      src="https://unpkg.com/moment@2.14.1/min/moment-with-locales.min.js"
      crossorigin></script>
    <script
      src="https://unpkg.com/lodash@4.17.20/lodash.min.js"
      crossorigin></script>
    <script
      src="https://unpkg.com/axios@0.27.2/dist/axios.min.js"
      crossorigin></script>
    <script
      src="https://unpkg.com/react-bootstrap-typeahead@5.1.4/dist/react-bootstrap-typeahead.min.js"
      crossorigin></script>
    <script
      src="https://unpkg.com/notyf@3.9.0/notyf.min.js"
      crossorigin></script>

    <!-- prop-types is required for react-dropzone package. -->
    <script
      src="https://unpkg.com/prop-types@15.6/prop-types.min.js"
      crossorigin></script>
    <script src="https://unpkg.com/react-dropzone@14.2.2" crossorigin></script>

    <script>
      const { useState, useRef, useEffect } = React;

      const {
        Modal,
        ButtonGroup,
        Button,
        Badge,
        Form,
        Spinner,
        Accordion,
        Card,
        OverlayTrigger,
        Popover,
        Row,
        Col,
        InputGroup,
        FormControl,
        useAccordionToggle,
        Table,
        Pagination
      } = ReactBootstrap;

      const { Typeahead, Highlighter } = ReactBootstrapTypeahead;

      const { default: Dropzone } = reactDropzone;
    </script>

    <script type="text/babel" src="./js/Config.js"></script>
    <script type="text/babel" src="./js/HighlightChange.js"></script>
    <script
      type="text/babel"
      src="./js/CoinWrapperSellLastBuyPrice.js"></script>
    <script type="text/babel" src="./js/CoinWrapperSetting.js"></script>

    <script type="text/babel" src="./js/CoinWrapperSellOrders.js"></script>
    <script type="text/babel" src="./js/CoinWrapperSellSignal.js"></script>
    <script type="text/babel" src="./js/CoinWrapperBuyOrders.js"></script>
    <script type="text/babel" src="./js/CoinWrapperBuySignal.js"></script>
    <script type="text/babel" src="./js/CoinWrapperTradingViews.js"></script>
    <script type="text/babel" src="./js/CoinWrapperTradingView.js"></script>
    <script type="text/babel" src="./js/SymbolManualTradeIcon.js"></script>
    <script type="text/babel" src="./js/CoinWrapperAction.js"></script>
    <script type="text/babel" src="./js/CoinWrapperBalance.js"></script>
    <script type="text/babel" src="./js/SymbolGridCalculator.js"></script>
    <script type="text/babel" src="./js/SymbolGridTradeArchiveIcon.js"></script>
    <script type="text/babel" src="./js/SymbolCancelIcon.js"></script>
    <script type="text/babel" src="./js/SymbolEnableActionIcon.js"></script>
    <script type="text/babel" src="./js/SymbolDeleteIcon.js"></script>
    <script type="text/babel" src="./js/SymbolEditLastBuyPriceIcon.js"></script>
    <script type="text/babel" src="./js/SymbolTriggerSellIcon.js"></script>
    <script type="text/babel" src="./js/SymbolTriggerBuyIcon.js"></script>
    <script
      type="text/babel"
      src="./js/SymbolSettingActionResetGridTrade.js"></script>
    <script
      type="text/babel"
      src="./js/SymbolSettingActionResetToGlobalSetting.js"></script>
    <script type="text/babel" src="./js/SymbolSettingActions.js"></script>
    <script
      type="text/babel"
      src="./js/SymbolSettingIconBotOptionsAutoTriggerBuy.js"></script>
    <script
      type="text/babel"
      src="./js/SymbolSettingIconBotOptions.js"></script>
    <script
      type="text/babel"
      src="./js/SymbolSettingIconTradingView.js"></script>
    <script type="text/babel" src="./js/SymbolSettingIconGridBuy.js"></script>
    <script type="text/babel" src="./js/SymbolSettingIconGridSell.js"></script>
    <script type="text/babel" src="./js/SymbolSettingIcon.js"></script>
    <script type="text/babel" src="./js/SymbolLogsIcon.js"></script>
    <script type="text/babel" src="./js/CoinWrapperSymbol.js"></script>
    <script type="text/babel" src="./js/CoinWrapper.js"></script>
    <script type="text/babel" src="./js/DustTransferIcon.js"></script>
    <script type="text/babel" src="./js/ManualTradeIcon.js"></script>
    <script
      type="text/babel"
      src="./js/SettingIconActionBackupConfirmModal.js"></script>
    <script
      type="text/babel"
      src="./js/SettingIconActionRestoreConfirmModal.js"></script>
    <script
      type="text/babel"
      src="./js/SettingIconActionRestoreSuccessModal.js"></script>
    <script type="text/babel" src="./js/SettingIconActions.js"></script>
    <script type="text/babel" src="./js/SettingIconGridSell.js"></script>
    <script type="text/babel" src="./js/SettingIconGridBuy.js"></script>
    <script type="text/babel" src="./js/SettingIconTradingView.js"></script>
    <script
      type="text/babel"
      src="./js/SettingIconBotOptionsTradingView.js"></script>
    <script type="text/babel" src="./js/SettingIconBotOptionsLogs.js"></script>
    <script
      type="text/babel"
      src="./js/SettingIconBotOptionsOrderLimit.js"></script>
    <script
      type="text/babel"
      src="./js/SettingIconBotOptionsAutoTriggerBuy.js"></script>
    <script
      type="text/babel"
      src="./js/SettingIconBotOptionsAuthentication.js"></script>
    <script type="text/babel" src="./js/SettingIconBotOptions.js"></script>

    <script
      type="text/babel"
      src="./js/SettingIconLastBuyPriceRemoveThreshold.js"></script>
    <script type="text/babel" src="./js/SettingIcon.js"></script>
    <script type="text/babel" src="./js/AccountWrapperAsset.js"></script>
    <script type="text/babel" src="./js/AccountWrapper.js"></script>
    <script
      type="text/babel"
      src="./js/QuoteAssetGridTradeArchiveIcon.js"></script>
    <script type="text/babel" src="./js/ProfitLossWrapper.js"></script>
    <script type="text/babel" src="./js/OrderStats.js"></script>
    <script type="text/babel" src="./js/Status.js"></script>
    <script type="text/babel" src="./js/Footer.js"></script>
    <script type="text/babel" src="./js/FilterIcon.js"></script>
    <script type="text/babel" src="./js/LockIcon.js"></script>
    <script type="text/babel" src="./js/UnlockIcon.js"></script>
    <script type="text/babel" src="./js/Header.js"></script>
    <script type="text/babel" src="./js/APIError.js"></script>
    <script type="text/babel" src="./js/LockScreen.js"></script>
    <script type="text/babel" src="./js/AppSorting.js"></script>
    <script type="text/babel" src="./js/AppLoading.js"></script>
    <script type="text/babel" src="./js/App.js"></script>
  </body>
</html>
