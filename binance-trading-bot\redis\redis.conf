#appendonly yes
#appendfilename "appendonly.aof"
#appendfsync everysec
#no-appendfsync-on-rewrite no
#auto-aof-rewrite-percentage 100
#auto-aof-rewrite-min-size 64mb
#aof-load-truncated yes

# Disable AOF by setting the appendonly configuration directive to no (it is the default value).
appendonly no
# Disable RDB snapshotting by commenting all of the save configuration directives
save ""

# Update the value to 0
tcp-keepalive 0

# TCP listen() backlog.
# In high requests-per-second environments you need an high backlog in order
# make sure to raise both the value of somaxconn and tcp_max_syn_backlog
tcp-backlog 65536

# # serve stale data if the sync is not complete
# slave-serve-stale-data yes
# # stop yourself from accidentally writing to the slave
# slave-read-only yes

# Once the limit is reached Redis will close all the new connections sending
# an error 'max number of clients reached'.
maxclients 10000

# memory size in bytes
maxmemory 1288490188
